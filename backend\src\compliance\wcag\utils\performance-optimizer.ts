/**
 * WCAG Performance Optimizer
 * Implements performance optimizations for enhanced WCAG checks
 * Targets 60-70% faster scan times through parallel execution, caching, and resource optimization
 */

import { Page } from 'puppeteer';
import { AdvancedLayoutAnalyzer } from './advanced-layout-analyzer';
import { AdvancedFocusTracker } from './advanced-focus-tracker';
import { WideGamutColorAnalyzer } from './wide-gamut-color-analyzer';
import EnhancedColorAnalyzer from './enhanced-color-analyzer';
import SmartCache from './smart-cache';
import { logger } from '../../../utils/logger';

export interface PerformanceOptimizationConfig {
  enableParallelExecution: boolean;
  enableSmartCaching: boolean;
  enableDynamicTimeouts: boolean;
  enableResourcePooling: boolean;
  enableEvidenceOptimization: boolean;
  maxParallelUtilities: number;
  baseTimeoutMs: number;
  cacheStrategy: 'aggressive' | 'balanced' | 'conservative';
}

export interface UtilityExecutionPlan {
  utilityName: string;
  method: string;
  priority: 'high' | 'medium' | 'low';
  estimatedDuration: number;
  dependencies: string[];
  canRunInParallel: boolean;
}

export interface OptimizedUtilityResult {
  utilityName: string;
  result: any;
  executionTime: number;
  cacheHit: boolean;
  memoryUsed: number;
}

export interface PerformanceMetrics {
  totalExecutionTime: number;
  parallelExecutionTime: number;
  cacheHitRate: number;
  memoryPeakMB: number;
  utilitiesExecuted: number;
  performanceGain: number;
}

/**
 * Performance optimizer for WCAG utility execution
 */
export class WCAGPerformanceOptimizer {
  private static instance: WCAGPerformanceOptimizer;
  private config: PerformanceOptimizationConfig;
  private smartCache = SmartCache.getInstance();
  private executionMetrics = new Map<string, PerformanceMetrics>();

  private constructor(config?: Partial<PerformanceOptimizationConfig>) {
    this.config = {
      enableParallelExecution: config?.enableParallelExecution ?? true,
      enableSmartCaching: config?.enableSmartCaching ?? true,
      enableDynamicTimeouts: config?.enableDynamicTimeouts ?? true,
      enableResourcePooling: config?.enableResourcePooling ?? true,
      enableEvidenceOptimization: config?.enableEvidenceOptimization ?? true,
      maxParallelUtilities: config?.maxParallelUtilities ?? 4,
      baseTimeoutMs: config?.baseTimeoutMs ?? 5000,
      cacheStrategy: config?.cacheStrategy ?? 'balanced',
    };
  }

  static getInstance(config?: Partial<PerformanceOptimizationConfig>): WCAGPerformanceOptimizer {
    if (!WCAGPerformanceOptimizer.instance) {
      WCAGPerformanceOptimizer.instance = new WCAGPerformanceOptimizer(config);
    }
    return WCAGPerformanceOptimizer.instance;
  }

  /**
   * Execute utilities with performance optimization
   */
  async executeOptimizedUtilities(
    page: Page,
    executionPlan: UtilityExecutionPlan[],
    checkId: string
  ): Promise<{
    results: OptimizedUtilityResult[];
    metrics: PerformanceMetrics;
  }> {
    const startTime = Date.now();
    const startMemory = this.getCurrentMemoryUsage();

    logger.debug(`🚀 Starting optimized utility execution for check: ${checkId}`);

    // Phase 1: Check cache for existing results
    const cachedResults = await this.checkCachedResults(page, executionPlan, checkId);
    const uncachedPlan = executionPlan.filter(plan => 
      !cachedResults.some(cached => cached.utilityName === plan.utilityName)
    );

    // Phase 2: Execute uncached utilities with optimization
    const executedResults = uncachedPlan.length > 0 
      ? await this.executeUtilitiesOptimized(page, uncachedPlan, checkId)
      : [];

    // Phase 3: Combine cached and executed results
    const allResults = [...cachedResults, ...executedResults];

    // Phase 4: Calculate performance metrics
    const endTime = Date.now();
    const endMemory = this.getCurrentMemoryUsage();
    
    const metrics: PerformanceMetrics = {
      totalExecutionTime: endTime - startTime,
      parallelExecutionTime: this.calculateParallelTime(executedResults),
      cacheHitRate: cachedResults.length / executionPlan.length,
      memoryPeakMB: Math.max(startMemory, endMemory),
      utilitiesExecuted: executionPlan.length,
      performanceGain: this.calculatePerformanceGain(executionPlan, endTime - startTime),
    };

    this.executionMetrics.set(checkId, metrics);

    logger.info(`✅ Optimized execution completed for ${checkId}: ${metrics.performanceGain.toFixed(1)}% faster`);

    return { results: allResults, metrics };
  }

  /**
   * Execute layout analysis utilities in parallel
   */
  async executeLayoutAnalysisOptimized(
    page: Page,
    checkId: string
  ): Promise<{
    responsiveLayout?: any;
    advancedSpacing?: any;
    contentAdaptation?: any;
    enhancedTargetSizes?: any;
    bypassMechanisms?: any;
  }> {
    const layoutAnalyzer = AdvancedLayoutAnalyzer.getInstance();
    
    if (!this.config.enableParallelExecution) {
      // Sequential execution (fallback)
      return {
        responsiveLayout: await layoutAnalyzer.analyzeResponsiveLayout(page),
        advancedSpacing: await layoutAnalyzer.analyzeAdvancedSpacing(page),
        contentAdaptation: await layoutAnalyzer.analyzeContentAdaptation(page),
        enhancedTargetSizes: await layoutAnalyzer.analyzeEnhancedTargetSizes(page),
      };
    }

    // Parallel execution with timeout management
    const timeout = this.calculateDynamicTimeout(page, 'layout');
    
    try {
      const [responsiveLayout, advancedSpacing, contentAdaptation, enhancedTargetSizes] = 
        await Promise.all([
          this.executeWithTimeout(layoutAnalyzer.analyzeResponsiveLayout(page), timeout),
          this.executeWithTimeout(layoutAnalyzer.analyzeAdvancedSpacing(page), timeout),
          this.executeWithTimeout(layoutAnalyzer.analyzeContentAdaptation(page), timeout),
          this.executeWithTimeout(layoutAnalyzer.analyzeEnhancedTargetSizes(page), timeout),
        ]);

      return { responsiveLayout, advancedSpacing, contentAdaptation, enhancedTargetSizes };
    } catch (error) {
      logger.warn(`⚠️ Parallel layout analysis failed for ${checkId}, falling back to sequential`);
      return this.executeLayoutAnalysisOptimized(page, checkId);
    }
  }

  /**
   * Execute focus management utilities in parallel
   */
  async executeFocusAnalysisOptimized(
    page: Page,
    checkId: string
  ): Promise<{
    advancedFocusVisibility?: any;
    advancedFocusOrder?: any;
    keyboardAccessibility?: any;
    keyboardTraps?: any;
    focusObstruction?: any;
  }> {
    const focusTracker = AdvancedFocusTracker.getAdvancedInstance();
    
    if (!this.config.enableParallelExecution) {
      // Sequential execution (fallback)
      return {
        advancedFocusVisibility: await focusTracker.analyzeAdvancedFocusVisibility(page),
        advancedFocusOrder: await focusTracker.analyzeAdvancedFocusOrder(page),
        keyboardAccessibility: await focusTracker.analyzeKeyboardAccessibility(page),
        keyboardTraps: await focusTracker.analyzeKeyboardTraps(page),
        focusObstruction: await focusTracker.analyzeFocusObstruction(page),
      };
    }

    // Parallel execution with timeout management
    const timeout = this.calculateDynamicTimeout(page, 'focus');
    
    try {
      const [advancedFocusVisibility, advancedFocusOrder, keyboardAccessibility, keyboardTraps, focusObstruction] = 
        await Promise.all([
          this.executeWithTimeout(focusTracker.analyzeAdvancedFocusVisibility(page), timeout),
          this.executeWithTimeout(focusTracker.analyzeAdvancedFocusOrder(page), timeout),
          this.executeWithTimeout(focusTracker.analyzeKeyboardAccessibility(page), timeout),
          this.executeWithTimeout(focusTracker.analyzeKeyboardTraps(page), timeout),
          this.executeWithTimeout(focusTracker.analyzeFocusObstruction(page), timeout),
        ]);

      return { advancedFocusVisibility, advancedFocusOrder, keyboardAccessibility, keyboardTraps, focusObstruction };
    } catch (error) {
      logger.warn(`⚠️ Parallel focus analysis failed for ${checkId}, falling back to sequential`);
      return this.executeFocusAnalysisOptimized(page, checkId);
    }
  }

  /**
   * Execute color analysis utilities in parallel
   */
  async executeColorAnalysisOptimized(
    page: Page,
    checkId: string
  ): Promise<{
    wideGamutContrast?: any;
    enhancedColorAnalysis?: any;
  }> {
    const wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();
    const enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
    
    if (!this.config.enableParallelExecution) {
      // Sequential execution (fallback)
      return {
        wideGamutContrast: await wideGamutAnalyzer.analyzeWideGamutContrast(page),
        enhancedColorAnalysis: await enhancedColorAnalyzer.analyzeAdvancedContrast(page),
      };
    }

    // Parallel execution with timeout management
    const timeout = this.calculateDynamicTimeout(page, 'color');
    
    try {
      const [wideGamutContrast, enhancedColorAnalysis] = await Promise.all([
        this.executeWithTimeout(wideGamutAnalyzer.analyzeWideGamutContrast(page), timeout),
        this.executeWithTimeout(enhancedColorAnalyzer.analyzeAdvancedContrast(page), timeout),
      ]);

      return { wideGamutContrast, enhancedColorAnalysis };
    } catch (error) {
      logger.warn(`⚠️ Parallel color analysis failed for ${checkId}, falling back to sequential`);
      return this.executeColorAnalysisOptimized(page, checkId);
    }
  }

  /**
   * Get performance metrics for a check
   */
  getPerformanceMetrics(checkId: string): PerformanceMetrics | undefined {
    return this.executionMetrics.get(checkId);
  }

  /**
   * Get overall performance statistics
   */
  getOverallPerformanceStats(): {
    averagePerformanceGain: number;
    averageCacheHitRate: number;
    averageExecutionTime: number;
    totalChecksOptimized: number;
  } {
    const metrics = Array.from(this.executionMetrics.values());
    
    if (metrics.length === 0) {
      return {
        averagePerformanceGain: 0,
        averageCacheHitRate: 0,
        averageExecutionTime: 0,
        totalChecksOptimized: 0,
      };
    }

    return {
      averagePerformanceGain: metrics.reduce((sum, m) => sum + m.performanceGain, 0) / metrics.length,
      averageCacheHitRate: metrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / metrics.length,
      averageExecutionTime: metrics.reduce((sum, m) => sum + m.totalExecutionTime, 0) / metrics.length,
      totalChecksOptimized: metrics.length,
    };
  }

  /**
   * Check for cached utility results
   */
  private async checkCachedResults(
    page: Page,
    executionPlan: UtilityExecutionPlan[],
    checkId: string
  ): Promise<OptimizedUtilityResult[]> {
    if (!this.config.enableSmartCaching) {
      return [];
    }

    const cachedResults: OptimizedUtilityResult[] = [];
    const pageUrl = page.url();
    const pageHash = await this.generatePageHash(page);

    for (const plan of executionPlan) {
      const cacheKey = `${plan.utilityName}:${plan.method}:${pageHash}`;
      const cached = await this.smartCache.getUtilityAnalysis(pageUrl, cacheKey);

      if (cached) {
        cachedResults.push({
          utilityName: plan.utilityName,
          result: cached,
          executionTime: 0,
          cacheHit: true,
          memoryUsed: 0,
        });
      }
    }

    return cachedResults;
  }

  /**
   * Execute utilities with optimization
   */
  private async executeUtilitiesOptimized(
    page: Page,
    executionPlan: UtilityExecutionPlan[],
    checkId: string
  ): Promise<OptimizedUtilityResult[]> {
    const results: OptimizedUtilityResult[] = [];

    if (this.config.enableParallelExecution && executionPlan.length > 1) {
      // Group utilities by dependencies and execute in parallel batches
      const batches = this.createExecutionBatches(executionPlan);

      for (const batch of batches) {
        const batchResults = await this.executeUtilityBatch(page, batch, checkId);
        results.push(...batchResults);
      }
    } else {
      // Sequential execution
      for (const plan of executionPlan) {
        const result = await this.executeUtility(page, plan, checkId);
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Execute a batch of utilities in parallel
   */
  private async executeUtilityBatch(
    page: Page,
    batch: UtilityExecutionPlan[],
    checkId: string
  ): Promise<OptimizedUtilityResult[]> {
    const promises = batch.map(plan => this.executeUtility(page, plan, checkId));

    try {
      return await Promise.all(promises);
    } catch (error) {
      logger.warn(`⚠️ Batch execution failed for ${checkId}, executing sequentially`);

      // Fallback to sequential execution
      const results: OptimizedUtilityResult[] = [];
      for (const plan of batch) {
        try {
          const result = await this.executeUtility(page, plan, checkId);
          results.push(result);
        } catch (utilityError) {
          logger.error(`❌ Utility ${plan.utilityName} failed: ${utilityError}`);
          // Continue with other utilities
        }
      }
      return results;
    }
  }

  /**
   * Execute a single utility with performance tracking
   */
  private async executeUtility(
    page: Page,
    plan: UtilityExecutionPlan,
    checkId: string
  ): Promise<OptimizedUtilityResult> {
    const startTime = Date.now();
    const startMemory = this.getCurrentMemoryUsage();

    try {
      const timeout = this.calculateDynamicTimeout(page, plan.utilityName);
      const result = await this.executeWithTimeout(
        this.callUtilityMethod(page, plan),
        timeout
      );

      const executionTime = Date.now() - startTime;
      const memoryUsed = this.getCurrentMemoryUsage() - startMemory;

      // Cache the result if caching is enabled
      if (this.config.enableSmartCaching) {
        await this.cacheUtilityResult(page, plan, result);
      }

      return {
        utilityName: plan.utilityName,
        result,
        executionTime,
        cacheHit: false,
        memoryUsed,
      };
    } catch (error) {
      logger.error(`❌ Utility execution failed: ${plan.utilityName} - ${error}`);
      throw error;
    }
  }

  /**
   * Calculate dynamic timeout based on page complexity and utility type
   */
  private calculateDynamicTimeout(page: Page, utilityType: string): number {
    if (!this.config.enableDynamicTimeouts) {
      return this.config.baseTimeoutMs;
    }

    // Base timeout multipliers by utility type
    const multipliers = {
      layout: 1.5,
      focus: 1.2,
      color: 1.0,
      default: 1.0,
    };

    const multiplier = multipliers[utilityType as keyof typeof multipliers] || multipliers.default;
    return Math.round(this.config.baseTimeoutMs * multiplier);
  }

  /**
   * Execute promise with timeout
   */
  private async executeWithTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Timeout after ${timeoutMs}ms`)), timeoutMs)
      ),
    ]);
  }

  /**
   * Get current memory usage in MB
   */
  private getCurrentMemoryUsage(): number {
    const usage = process.memoryUsage();
    return Math.round(usage.heapUsed / 1024 / 1024);
  }

  /**
   * Generate page hash for caching
   */
  private async generatePageHash(page: Page): Promise<string> {
    try {
      const content = await page.content();
      const url = page.url();
      return Buffer.from(`${url}:${content.length}`).toString('base64').slice(0, 16);
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Calculate performance gain percentage
   */
  private calculatePerformanceGain(plan: UtilityExecutionPlan[], actualTime: number): number {
    const estimatedSequentialTime = plan.reduce((sum, p) => sum + p.estimatedDuration, 0);
    return estimatedSequentialTime > 0
      ? Math.max(0, ((estimatedSequentialTime - actualTime) / estimatedSequentialTime) * 100)
      : 0;
  }

  /**
   * Calculate parallel execution time
   */
  private calculateParallelTime(results: OptimizedUtilityResult[]): number {
    return Math.max(...results.map(r => r.executionTime), 0);
  }

  /**
   * Create execution batches based on dependencies
   */
  private createExecutionBatches(plan: UtilityExecutionPlan[]): UtilityExecutionPlan[][] {
    // Simple batching - group by parallel capability
    const parallelUtilities = plan.filter(p => p.canRunInParallel);
    const sequentialUtilities = plan.filter(p => !p.canRunInParallel);

    const batches: UtilityExecutionPlan[][] = [];

    // Add parallel utilities in batches of maxParallelUtilities
    for (let i = 0; i < parallelUtilities.length; i += this.config.maxParallelUtilities) {
      batches.push(parallelUtilities.slice(i, i + this.config.maxParallelUtilities));
    }

    // Add sequential utilities one by one
    sequentialUtilities.forEach(utility => {
      batches.push([utility]);
    });

    return batches;
  }

  /**
   * Call utility method dynamically
   */
  private async callUtilityMethod(page: Page, plan: UtilityExecutionPlan): Promise<any> {
    // This would be implemented to dynamically call the appropriate utility method
    // For now, return a placeholder
    return { placeholder: true, utility: plan.utilityName, method: plan.method };
  }

  /**
   * Cache utility result
   */
  private async cacheUtilityResult(page: Page, plan: UtilityExecutionPlan, result: any): Promise<void> {
    try {
      const pageUrl = page.url();
      const pageHash = await this.generatePageHash(page);
      const cacheKey = `${plan.utilityName}:${plan.method}:${pageHash}`;

      await this.smartCache.cacheUtilityAnalysis(pageUrl, cacheKey, result);
    } catch (error) {
      logger.warn(`⚠️ Failed to cache utility result: ${error}`);
    }
  }
}
