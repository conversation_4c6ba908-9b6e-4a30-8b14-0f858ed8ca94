/**
 * Batch Performance Optimizer for WCAG Checks
 * Implements batch optimization strategies for multiple enhanced checks
 * Targets 60-70% faster scan times through intelligent batching and resource management
 */

import { Page } from 'puppeteer';
import { WCAGPerformanceOptimizer } from './performance-optimizer';
import { AdvancedLayoutAnalyzer } from './advanced-layout-analyzer';
import { AdvancedFocusTracker } from './advanced-focus-tracker';
import { WideGamutColorAnalyzer } from './wide-gamut-color-analyzer';
import EnhancedColorAnalyzer from './enhanced-color-analyzer';
import SmartCache from './smart-cache';
import { logger } from '../../../utils/logger';

export interface BatchOptimizationConfig {
  enableBatchExecution: boolean;
  enableCrossCheckCaching: boolean;
  enableResourceSharing: boolean;
  enablePredictivePreloading: boolean;
  maxBatchSize: number;
  batchTimeoutMs: number;
  cacheStrategy: 'aggressive' | 'balanced' | 'conservative';
}

export interface CheckBatch {
  checkId: string;
  checkName: string;
  utilityRequirements: {
    layout: string[];
    focus: string[];
    color: string[];
  };
  priority: 'high' | 'medium' | 'low';
  estimatedDuration: number;
}

export interface BatchExecutionResult {
  checkId: string;
  results: {
    layout?: any;
    focus?: any;
    color?: any;
  };
  executionTime: number;
  cacheHits: number;
  memoryUsed: number;
}

export interface BatchPerformanceMetrics {
  totalBatchTime: number;
  individualExecutionTime: number;
  performanceGain: number;
  cacheEfficiency: number;
  resourceUtilization: number;
  checksProcessed: number;
}

/**
 * Batch performance optimizer for multiple WCAG checks
 */
export class BatchPerformanceOptimizer {
  private static instance: BatchPerformanceOptimizer;
  private config: BatchOptimizationConfig;
  private performanceOptimizer = WCAGPerformanceOptimizer.getInstance();
  private smartCache = SmartCache.getInstance();
  
  // Shared utility instances for batch processing
  private layoutAnalyzer = AdvancedLayoutAnalyzer.getInstance();
  private focusTracker = AdvancedFocusTracker.getAdvancedInstance();
  private colorAnalyzer = WideGamutColorAnalyzer.getInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();

  private constructor(config?: Partial<BatchOptimizationConfig>) {
    this.config = {
      enableBatchExecution: config?.enableBatchExecution ?? true,
      enableCrossCheckCaching: config?.enableCrossCheckCaching ?? true,
      enableResourceSharing: config?.enableResourceSharing ?? true,
      enablePredictivePreloading: config?.enablePredictivePreloading ?? true,
      maxBatchSize: config?.maxBatchSize ?? 8,
      batchTimeoutMs: config?.batchTimeoutMs ?? 30000,
      cacheStrategy: config?.cacheStrategy ?? 'balanced',
    };
  }

  static getInstance(config?: Partial<BatchOptimizationConfig>): BatchPerformanceOptimizer {
    if (!BatchPerformanceOptimizer.instance) {
      BatchPerformanceOptimizer.instance = new BatchPerformanceOptimizer(config);
    }
    return BatchPerformanceOptimizer.instance;
  }

  /**
   * Execute multiple checks with batch optimization
   */
  async executeBatchOptimized(
    page: Page,
    checkBatches: CheckBatch[]
  ): Promise<{
    results: BatchExecutionResult[];
    metrics: BatchPerformanceMetrics;
  }> {
    const startTime = Date.now();
    logger.info(`🚀 Starting batch optimization for ${checkBatches.length} checks`);

    // Phase 1: Analyze and group checks by utility requirements
    const optimizedBatches = this.optimizeBatchGrouping(checkBatches);

    // Phase 2: Pre-load shared resources and cache
    await this.preloadSharedResources(page, optimizedBatches);

    // Phase 3: Execute batches with shared utility results
    const results: BatchExecutionResult[] = [];
    
    for (const batch of optimizedBatches) {
      const batchResults = await this.executeBatch(page, batch);
      results.push(...batchResults);
    }

    // Phase 4: Calculate performance metrics
    const endTime = Date.now();
    const metrics = this.calculateBatchMetrics(checkBatches, results, endTime - startTime);

    logger.info(`✅ Batch optimization completed: ${metrics.performanceGain.toFixed(1)}% faster`);

    return { results, metrics };
  }

  /**
   * Execute layout analysis for multiple checks with shared results
   */
  async executeSharedLayoutAnalysis(
    page: Page,
    checkIds: string[]
  ): Promise<Map<string, any>> {
    const results = new Map<string, any>();

    if (!this.config.enableResourceSharing) {
      // Individual execution for each check
      for (const checkId of checkIds) {
        const result = await this.performanceOptimizer.executeLayoutAnalysisOptimized(page, checkId);
        results.set(checkId, result);
      }
      return results;
    }

    // Shared execution - run once, use for all checks
    logger.debug(`🔄 Executing shared layout analysis for ${checkIds.length} checks`);

    try {
      // Execute all layout analysis methods in parallel
      const [responsiveLayout, advancedSpacing, contentAdaptation, enhancedTargetSizes, bypassMechanisms] = 
        await Promise.all([
          this.layoutAnalyzer.analyzeResponsiveLayout(page),
          this.layoutAnalyzer.analyzeAdvancedSpacing(page),
          this.layoutAnalyzer.analyzeContentAdaptation(page),
          this.layoutAnalyzer.analyzeEnhancedTargetSizes(page),
          this.layoutAnalyzer.analyzeBypassMechanisms(page),
        ]);

      // Share results across all checks
      const sharedResults = {
        responsiveLayout,
        advancedSpacing,
        contentAdaptation,
        enhancedTargetSizes,
        bypassMechanisms,
      };

      checkIds.forEach(checkId => {
        results.set(checkId, sharedResults);
      });

      // Cache shared results for future use
      if (this.config.enableCrossCheckCaching) {
        await this.cacheSharedResults(page, 'layout', sharedResults);
      }

    } catch (error) {
      logger.warn(`⚠️ Shared layout analysis failed, falling back to individual execution`);
      
      // Fallback to individual execution
      for (const checkId of checkIds) {
        try {
          const result = await this.performanceOptimizer.executeLayoutAnalysisOptimized(page, checkId);
          results.set(checkId, result);
        } catch (checkError) {
          logger.error(`❌ Layout analysis failed for ${checkId}: ${checkError}`);
        }
      }
    }

    return results;
  }

  /**
   * Execute focus analysis for multiple checks with shared results
   */
  async executeSharedFocusAnalysis(
    page: Page,
    checkIds: string[]
  ): Promise<Map<string, any>> {
    const results = new Map<string, any>();

    if (!this.config.enableResourceSharing) {
      // Individual execution for each check
      for (const checkId of checkIds) {
        const result = await this.performanceOptimizer.executeFocusAnalysisOptimized(page, checkId);
        results.set(checkId, result);
      }
      return results;
    }

    // Shared execution - run once, use for all checks
    logger.debug(`🔄 Executing shared focus analysis for ${checkIds.length} checks`);

    try {
      // Execute all focus analysis methods in parallel
      const [advancedFocusVisibility, advancedFocusOrder, keyboardAccessibility, keyboardTraps, focusObstruction] = 
        await Promise.all([
          this.focusTracker.analyzeAdvancedFocusVisibility(page),
          this.focusTracker.analyzeAdvancedFocusOrder(page),
          this.focusTracker.analyzeKeyboardAccessibility(page),
          this.focusTracker.analyzeKeyboardTraps(page),
          this.focusTracker.analyzeFocusObstruction(page),
        ]);

      // Share results across all checks
      const sharedResults = {
        advancedFocusVisibility,
        advancedFocusOrder,
        keyboardAccessibility,
        keyboardTraps,
        focusObstruction,
      };

      checkIds.forEach(checkId => {
        results.set(checkId, sharedResults);
      });

      // Cache shared results for future use
      if (this.config.enableCrossCheckCaching) {
        await this.cacheSharedResults(page, 'focus', sharedResults);
      }

    } catch (error) {
      logger.warn(`⚠️ Shared focus analysis failed, falling back to individual execution`);
      
      // Fallback to individual execution
      for (const checkId of checkIds) {
        try {
          const result = await this.performanceOptimizer.executeFocusAnalysisOptimized(page, checkId);
          results.set(checkId, result);
        } catch (checkError) {
          logger.error(`❌ Focus analysis failed for ${checkId}: ${checkError}`);
        }
      }
    }

    return results;
  }

  /**
   * Execute color analysis for multiple checks with shared results
   */
  async executeSharedColorAnalysis(
    page: Page,
    checkIds: string[]
  ): Promise<Map<string, any>> {
    const results = new Map<string, any>();

    if (!this.config.enableResourceSharing) {
      // Individual execution for each check
      for (const checkId of checkIds) {
        const result = await this.performanceOptimizer.executeColorAnalysisOptimized(page, checkId);
        results.set(checkId, result);
      }
      return results;
    }

    // Shared execution - run once, use for all checks
    logger.debug(`🔄 Executing shared color analysis for ${checkIds.length} checks`);

    try {
      // Execute all color analysis methods in parallel
      const [wideGamutContrast, enhancedColorAnalysis] = await Promise.all([
        this.colorAnalyzer.analyzeWideGamutContrast(page),
        this.enhancedColorAnalyzer.analyzeAdvancedContrast(page),
      ]);

      // Share results across all checks
      const sharedResults = {
        wideGamutContrast,
        enhancedColorAnalysis,
      };

      checkIds.forEach(checkId => {
        results.set(checkId, sharedResults);
      });

      // Cache shared results for future use
      if (this.config.enableCrossCheckCaching) {
        await this.cacheSharedResults(page, 'color', sharedResults);
      }

    } catch (error) {
      logger.warn(`⚠️ Shared color analysis failed, falling back to individual execution`);
      
      // Fallback to individual execution
      for (const checkId of checkIds) {
        try {
          const result = await this.performanceOptimizer.executeColorAnalysisOptimized(page, checkId);
          results.set(checkId, result);
        } catch (checkError) {
          logger.error(`❌ Color analysis failed for ${checkId}: ${checkError}`);
        }
      }
    }

    return results;
  }

  /**
   * Optimize batch grouping based on utility requirements
   */
  private optimizeBatchGrouping(checkBatches: CheckBatch[]): CheckBatch[][] {
    if (!this.config.enableBatchExecution) {
      return checkBatches.map(batch => [batch]);
    }

    // Group checks by similar utility requirements
    const groups = new Map<string, CheckBatch[]>();

    for (const batch of checkBatches) {
      const key = this.generateGroupingKey(batch);
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(batch);
    }

    // Split large groups into smaller batches
    const optimizedBatches: CheckBatch[][] = [];

    for (const group of groups.values()) {
      for (let i = 0; i < group.length; i += this.config.maxBatchSize) {
        optimizedBatches.push(group.slice(i, i + this.config.maxBatchSize));
      }
    }

    return optimizedBatches;
  }

  /**
   * Pre-load shared resources and cache
   */
  private async preloadSharedResources(page: Page, batches: CheckBatch[][]): Promise<void> {
    if (!this.config.enablePredictivePreloading) {
      return;
    }

    logger.debug('🔄 Pre-loading shared resources for batch execution');

    try {
      // Pre-load common page analysis
      await Promise.all([
        page.evaluate(() => document.readyState),
        page.evaluate(() => document.documentElement.scrollHeight),
        page.evaluate(() => window.innerWidth),
        page.evaluate(() => window.innerHeight),
      ]);
    } catch (error) {
      logger.warn(`⚠️ Resource pre-loading failed: ${error}`);
    }
  }

  /**
   * Execute a single batch of checks
   */
  private async executeBatch(page: Page, batch: CheckBatch[]): Promise<BatchExecutionResult[]> {
    const results: BatchExecutionResult[] = [];
    const startTime = Date.now();

    // Group checks by utility type for shared execution
    const layoutChecks = batch.filter(b => b.utilityRequirements.layout.length > 0);
    const focusChecks = batch.filter(b => b.utilityRequirements.focus.length > 0);
    const colorChecks = batch.filter(b => b.utilityRequirements.color.length > 0);

    try {
      // Execute shared utility analyses
      const [layoutResults, focusResults, colorResults] = await Promise.all([
        layoutChecks.length > 0
          ? this.executeSharedLayoutAnalysis(page, layoutChecks.map(c => c.checkId))
          : Promise.resolve(new Map()),
        focusChecks.length > 0
          ? this.executeSharedFocusAnalysis(page, focusChecks.map(c => c.checkId))
          : Promise.resolve(new Map()),
        colorChecks.length > 0
          ? this.executeSharedColorAnalysis(page, colorChecks.map(c => c.checkId))
          : Promise.resolve(new Map()),
      ]);

      // Combine results for each check
      for (const check of batch) {
        const checkStartTime = Date.now();
        const checkStartMemory = this.getCurrentMemoryUsage();

        const result: BatchExecutionResult = {
          checkId: check.checkId,
          results: {
            layout: layoutResults.get(check.checkId),
            focus: focusResults.get(check.checkId),
            color: colorResults.get(check.checkId),
          },
          executionTime: Date.now() - checkStartTime,
          cacheHits: this.calculateCacheHits(check, layoutResults, focusResults, colorResults),
          memoryUsed: this.getCurrentMemoryUsage() - checkStartMemory,
        };

        results.push(result);
      }

    } catch (error) {
      logger.error(`❌ Batch execution failed: ${error}`);

      // Fallback to individual execution
      for (const check of batch) {
        try {
          const individualResult = await this.executeIndividualCheck(page, check);
          results.push(individualResult);
        } catch (checkError) {
          logger.error(`❌ Individual check execution failed for ${check.checkId}: ${checkError}`);
        }
      }
    }

    const batchTime = Date.now() - startTime;
    logger.debug(`✅ Batch completed in ${batchTime}ms for ${batch.length} checks`);

    return results;
  }

  /**
   * Execute individual check (fallback)
   */
  private async executeIndividualCheck(page: Page, check: CheckBatch): Promise<BatchExecutionResult> {
    const startTime = Date.now();
    const startMemory = this.getCurrentMemoryUsage();

    const results: any = {};

    // Execute utilities individually
    if (check.utilityRequirements.layout.length > 0) {
      results.layout = await this.performanceOptimizer.executeLayoutAnalysisOptimized(page, check.checkId);
    }

    if (check.utilityRequirements.focus.length > 0) {
      results.focus = await this.performanceOptimizer.executeFocusAnalysisOptimized(page, check.checkId);
    }

    if (check.utilityRequirements.color.length > 0) {
      results.color = await this.performanceOptimizer.executeColorAnalysisOptimized(page, check.checkId);
    }

    return {
      checkId: check.checkId,
      results,
      executionTime: Date.now() - startTime,
      cacheHits: 0,
      memoryUsed: this.getCurrentMemoryUsage() - startMemory,
    };
  }

  /**
   * Calculate batch performance metrics
   */
  private calculateBatchMetrics(
    originalBatches: CheckBatch[],
    results: BatchExecutionResult[],
    totalTime: number
  ): BatchPerformanceMetrics {
    const estimatedIndividualTime = originalBatches.reduce((sum, batch) => sum + batch.estimatedDuration, 0);
    const actualTime = results.reduce((sum, result) => sum + result.executionTime, 0);
    const totalCacheHits = results.reduce((sum, result) => sum + result.cacheHits, 0);
    const totalPossibleCacheHits = results.length * 3; // Assuming 3 utility types per check

    return {
      totalBatchTime: totalTime,
      individualExecutionTime: actualTime,
      performanceGain: estimatedIndividualTime > 0
        ? ((estimatedIndividualTime - totalTime) / estimatedIndividualTime) * 100
        : 0,
      cacheEfficiency: totalPossibleCacheHits > 0
        ? (totalCacheHits / totalPossibleCacheHits) * 100
        : 0,
      resourceUtilization: this.calculateResourceUtilization(results),
      checksProcessed: results.length,
    };
  }

  /**
   * Generate grouping key for batch optimization
   */
  private generateGroupingKey(batch: CheckBatch): string {
    const layout = batch.utilityRequirements.layout.sort().join(',');
    const focus = batch.utilityRequirements.focus.sort().join(',');
    const color = batch.utilityRequirements.color.sort().join(',');
    return `${layout}|${focus}|${color}`;
  }

  /**
   * Cache shared results for cross-check reuse
   */
  private async cacheSharedResults(page: Page, utilityType: string, results: any): Promise<void> {
    try {
      const pageUrl = page.url();
      const cacheKey = `shared:${utilityType}:${Date.now()}`;
      await this.smartCache.cacheUtilityAnalysis(pageUrl, cacheKey, results);
    } catch (error) {
      logger.warn(`⚠️ Failed to cache shared results: ${error}`);
    }
  }

  /**
   * Calculate cache hits for a check
   */
  private calculateCacheHits(
    check: CheckBatch,
    layoutResults: Map<string, any>,
    focusResults: Map<string, any>,
    colorResults: Map<string, any>
  ): number {
    let hits = 0;

    if (check.utilityRequirements.layout.length > 0 && layoutResults.has(check.checkId)) hits++;
    if (check.utilityRequirements.focus.length > 0 && focusResults.has(check.checkId)) hits++;
    if (check.utilityRequirements.color.length > 0 && colorResults.has(check.checkId)) hits++;

    return hits;
  }

  /**
   * Calculate resource utilization
   */
  private calculateResourceUtilization(results: BatchExecutionResult[]): number {
    const totalMemory = results.reduce((sum, result) => sum + result.memoryUsed, 0);
    const averageMemory = results.length > 0 ? totalMemory / results.length : 0;

    // Normalize to percentage (assuming 100MB as baseline)
    return Math.min(100, (averageMemory / 100) * 100);
  }

  /**
   * Get current memory usage in MB
   */
  private getCurrentMemoryUsage(): number {
    const usage = process.memoryUsage();
    return Math.round(usage.heapUsed / 1024 / 1024);
  }
}
