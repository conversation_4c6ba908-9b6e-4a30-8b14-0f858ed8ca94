/**
 * WCAG-040: Reflow Check
 * Success Criterion: 1.4.10 Reflow (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ReflowConfig extends EnhancedCheckConfig {
  enableLayoutAnalysis?: boolean;
  enableResponsiveDesignValidation?: boolean;
  enableFrameworkOptimization?: boolean;
  checkMultipleViewports?: boolean;
  enableContentAdaptationAnalysis?: boolean;
  enableThirdPartyLibraries?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableAdvancedColorSpaces?: boolean;
  enableAdvancedLayoutAnalysis?: boolean;
  enablePerformanceOptimization?: boolean;
}

export class ReflowCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();

  async performCheck(config: ReflowConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ReflowConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableLayoutAnalysis: true,
      enableResponsiveDesignValidation: true,
      enableFrameworkOptimization: true,
      checkMultipleViewports: true,
      enableContentAdaptationAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-040',
      'Reflow',
      'perceivable',
      0.0535,
      'AA',
      enhancedConfig,
      this.executeReflowCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with reflow analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-041',
        ruleName: 'Reflow',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'reflow-analysis',
          responsiveDesignValidation: true,
          viewportTesting: true,
          horizontalScrollingDetection: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeReflowCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Test reflow at 320px width (400% zoom equivalent)
    const originalViewport = page.viewport();
    
    try {
      // Set viewport to 320px width for reflow testing
      await page.setViewport({ width: 320, height: 568 });
      
      const reflowAnalysis = await page.evaluate(() => {
        const problematicElements: Array<{
          selector: string;
          tagName: string;
          width: number;
          scrollWidth: number;
          hasHorizontalScroll: boolean;
          overflowX: string;
          position: string;
          minWidth: string;
          maxWidth: string;
          issues: string[];
          severity: 'error' | 'warning' | 'info';
        }> = [];

        // Check document-level horizontal scroll
        const documentHasHorizontalScroll = document.documentElement.scrollWidth > document.documentElement.clientWidth;
        
        // Get all elements that might cause reflow issues
        const allElements = document.querySelectorAll('*');
        
        allElements.forEach((element, index) => {
          const computedStyle = window.getComputedStyle(element);
          const rect = element.getBoundingClientRect();
          
          // Skip very small elements
          if (rect.width < 10 || rect.height < 10) return;
          
          const issues: string[] = [];
          let severity: 'error' | 'warning' | 'info' = 'info';
          
          const overflowX = computedStyle.overflowX;
          const position = computedStyle.position;
          const minWidth = computedStyle.minWidth;
          const maxWidth = computedStyle.maxWidth;
          const width = computedStyle.width;
          
          // Check for horizontal scrolling
          const hasHorizontalScroll = element.scrollWidth > element.clientWidth;
          if (hasHorizontalScroll && overflowX !== 'hidden') {
            issues.push('Element has horizontal scroll');
            severity = 'error';
          }
          
          // Check for fixed widths that exceed 320px
          if (width.includes('px')) {
            const widthValue = parseFloat(width);
            if (widthValue > 320) {
              issues.push(`Fixed width (${width}) exceeds 320px`);
              severity = 'warning';
            }
          }
          
          // Check for minimum widths that might prevent reflow
          if (minWidth.includes('px')) {
            const minWidthValue = parseFloat(minWidth);
            if (minWidthValue > 300) {
              issues.push(`Min-width (${minWidth}) may prevent reflow`);
              severity = 'warning';
            }
          }
          
          // Check for elements extending beyond viewport
          if (rect.right > 320) {
            issues.push('Element extends beyond 320px viewport');
            severity = 'error';
          }
          
          // Check for absolute/fixed positioning that might cause issues
          if (position === 'absolute' || position === 'fixed') {
            const left = computedStyle.left;
            const right = computedStyle.right;
            if (left.includes('px') && parseFloat(left) > 300) {
              issues.push('Absolute positioning may cause horizontal scroll');
              severity = 'warning';
            }
          }
          
          // Check for overflow hidden that might clip content
          if (overflowX === 'hidden' && hasHorizontalScroll) {
            issues.push('Overflow hidden may clip content during reflow');
            severity = 'warning';
          }
          
          // Check for tables that might not reflow properly
          if (element.tagName === 'TABLE') {
            const tableLayout = computedStyle.tableLayout;
            if (tableLayout === 'fixed') {
              issues.push('Fixed table layout may prevent reflow');
              severity = 'warning';
            }
          }
          
          // Check for flex/grid containers with nowrap
          const display = computedStyle.display;
          if (display.includes('flex')) {
            const flexWrap = computedStyle.flexWrap;
            if (flexWrap === 'nowrap' && element.children.length > 1) {
              issues.push('Flex container with nowrap may prevent reflow');
              severity = 'warning';
            }
          }
          
          if (issues.length > 0) {
            problematicElements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              width: rect.width,
              scrollWidth: element.scrollWidth,
              hasHorizontalScroll,
              overflowX,
              position,
              minWidth,
              maxWidth,
              issues,
              severity,
            });
          }
        });

        // Check for specific problematic CSS patterns
        const styleSheets = Array.from(document.styleSheets);
        const cssIssues: string[] = [];
        
        try {
          styleSheets.forEach(sheet => {
            if (sheet.cssRules) {
              Array.from(sheet.cssRules).forEach(rule => {
                const cssText = rule.cssText || '';
                
                // Check for fixed widths in CSS
                if (/width:\s*[3-9]\d{2,}px/.test(cssText)) {
                  cssIssues.push('CSS contains large fixed widths');
                }
                
                // Check for white-space: nowrap
                if (/white-space:\s*nowrap/.test(cssText)) {
                  cssIssues.push('CSS uses white-space: nowrap');
                }
                
                // Check for overflow-x: scroll/auto on body/html
                if (/(body|html).*overflow-x:\s*(scroll|auto)/.test(cssText)) {
                  cssIssues.push('CSS enables horizontal scrolling on body/html');
                }
              });
            }
          });
        } catch (e) {
          // Cross-origin stylesheets may not be accessible
        }

        return {
          problematicElements,
          documentHasHorizontalScroll,
          cssIssues,
          totalElements: allElements.length,
          problematicCount: problematicElements.length,
          errorCount: problematicElements.filter(el => el.severity === 'error').length,
          warningCount: problematicElements.filter(el => el.severity === 'warning').length,
          viewportWidth: window.innerWidth,
          documentWidth: document.documentElement.scrollWidth,
        };
      });

      let score = 100;
      const elementCount = reflowAnalysis.problematicCount;
      const scanDuration = Date.now() - startTime;

      // Major deduction for document-level horizontal scroll
      if (reflowAnalysis.documentHasHorizontalScroll) {
        score -= 30;
        issues.push('Page requires horizontal scrolling at 320px width');
        
        evidence.push({
          type: 'text',
          description: 'Page requires horizontal scrolling at 320px width',
          value: `Document width: ${reflowAnalysis.documentWidth}px, Viewport: ${reflowAnalysis.viewportWidth}px`,
          selector: 'html',
          elementCount: 1,
          affectedSelectors: ['html'],
          severity: 'error',
          fixExample: {
            before: 'Page content extends beyond 320px requiring horizontal scroll',
            after: 'Page content reflows to fit within 320px width',
            description: 'Implement responsive design to prevent horizontal scrolling',
            codeExample: this.getCodeExample('document_reflow'),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/reflow.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/C32',
              'https://www.w3.org/WAI/WCAG21/Techniques/C31'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              documentWidth: reflowAnalysis.documentWidth,
              viewportWidth: reflowAnalysis.viewportWidth,
              hasHorizontalScroll: reflowAnalysis.documentHasHorizontalScroll,
            },
          },
        });
      }

      // Evaluate individual element issues
      if (elementCount > 0) {
        reflowAnalysis.problematicElements.forEach((element) => {
          const deduction = element.severity === 'error' ? 10 : 
                           element.severity === 'warning' ? 5 : 2;
          score = Math.max(0, score - deduction);
        });

        issues.push(`${elementCount} elements with reflow issues found`);
        if (reflowAnalysis.errorCount > 0) {
          issues.push(`${reflowAnalysis.errorCount} elements cause horizontal scrolling`);
        }

        reflowAnalysis.problematicElements.forEach((element) => {
          evidence.push({
            type: 'code',
            description: `Reflow issue: ${element.issues.join(', ')}`,
            value: `Width: ${element.width}px, Scroll: ${element.scrollWidth}px`,
            selector: element.selector,
            elementCount: 1,
            affectedSelectors: [element.selector],
            severity: element.severity,
            fixExample: {
              before: this.getBeforeExample(element),
              after: this.getAfterExample(element),
              description: this.getFixDescription(element.issues),
              codeExample: this.getCodeExample(element.issues[0] || 'general'),
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/reflow.html',
                'https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Flexible_Box_Layout',
                'https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Grid_Layout'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                width: element.width,
                scrollWidth: element.scrollWidth,
                hasHorizontalScroll: element.hasHorizontalScroll,
                overflowX: element.overflowX,
                position: element.position,
                issues: element.issues,
              },
            },
          });
        });
      }

      // CSS issues
      if (reflowAnalysis.cssIssues.length > 0) {
        score = Math.max(0, score - (reflowAnalysis.cssIssues.length * 5));
        issues.push(`CSS contains ${reflowAnalysis.cssIssues.length} reflow-preventing patterns`);
      }

      // Add recommendations
      recommendations.push('Use responsive design techniques (flexbox, grid, media queries)');
      recommendations.push('Avoid fixed widths larger than 320px');
      recommendations.push('Use relative units (%, em, rem) instead of fixed pixels');
      recommendations.push('Test content reflow at 400% zoom (320px width)');
      recommendations.push('Ensure content is readable without horizontal scrolling');

    } finally {
      // Restore original viewport
      if (originalViewport) {
        await page.setViewport(originalViewport);
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.issues.includes('Fixed width')) {
      return `<div style="width: ${element.width}px;">Fixed width content</div>`;
    }
    if (element.issues.includes('horizontal scroll')) {
      return '<div style="overflow-x: auto; white-space: nowrap;">Content that scrolls horizontally</div>';
    }
    return `<${element.tagName} style="width: ${element.width}px;">Content</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.issues.includes('Fixed width')) {
      return '<div style="max-width: 100%;">Responsive content</div>';
    }
    if (element.issues.includes('horizontal scroll')) {
      return '<div style="word-wrap: break-word;">Content that wraps properly</div>';
    }
    return `<${element.tagName} style="max-width: 100%;">Responsive content</${element.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('Fixed width')) {
      return 'Use responsive width instead of fixed pixels';
    }
    if (issues.includes('horizontal scroll')) {
      return 'Enable content wrapping and avoid horizontal scroll';
    }
    if (issues.includes('Min-width')) {
      return 'Reduce or remove minimum width constraints';
    }
    return 'Implement responsive design for proper content reflow';
  }

  private getCodeExample(issueType: string): string {
    switch (issueType) {
      case 'document_reflow':
        return `
/* Before: Fixed layout causing horizontal scroll */
.container {
  width: 1200px;
  margin: 0 auto;
}

/* After: Responsive layout */
.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 320px) {
  .container {
    padding: 0 10px;
  }
}
        `;
      case 'Fixed width':
        return `
/* Before: Fixed width elements */
.sidebar {
  width: 300px;
  float: left;
}
.content {
  width: 800px;
  float: right;
}

/* After: Flexible layout */
.container {
  display: flex;
  flex-wrap: wrap;
}
.sidebar {
  flex: 0 0 300px;
  min-width: 0;
}
.content {
  flex: 1;
  min-width: 0;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  .sidebar {
    flex: none;
    width: 100%;
  }
}
        `;
      case 'Element has horizontal scroll':
        return `
/* Before: Content that causes horizontal scroll */
.text-content {
  white-space: nowrap;
  overflow-x: auto;
}

/* After: Content that wraps properly */
.text-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* For code blocks that need horizontal scroll */
.code-block {
  overflow-x: auto;
  max-width: 100%;
  white-space: pre;
}
        `;
      default:
        return `
/* General responsive design principles */
* {
  box-sizing: border-box;
}

.responsive-container {
  max-width: 100%;
  padding: 0 1rem;
}

/* Use flexible layouts */
.flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.flex-item {
  flex: 1;
  min-width: 0; /* Allows flex items to shrink below content size */
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}
        `;
    }
  }
}
