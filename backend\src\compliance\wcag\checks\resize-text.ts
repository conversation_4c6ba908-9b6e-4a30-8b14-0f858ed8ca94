/**
 * WCAG-037: Resize Text Check
 * Success Criterion: 1.4.4 Resize Text (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WCAGPerformanceOptimizer } from '../utils/performance-optimizer';

// Third-party color libraries for contrast preservation analysis
let getContrastLib: any = null;
let ColorJSLib: any = null;

try {
  getContrastLib = require('get-contrast');
} catch (error) {
  // Fallback to built-in analysis
}

try {
  ColorJSLib = require('colorjs.io').default;
} catch (error) {
  // Fallback to built-in analysis
}

export interface ResizeTextConfig extends EnhancedCheckConfig {
  enableLayoutAnalysis?: boolean;
  enableEnhancedColorAnalysis?: boolean;
  enableModernFrameworkOptimization?: boolean;
  enableThirdPartyLibraries?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableContrastPreservationAnalysis?: boolean;
}

export class ResizeTextCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();
  private performanceOptimizer = WCAGPerformanceOptimizer.getInstance();

  async performCheck(config: ResizeTextConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ResizeTextConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableLayoutAnalysis: true,
      enableEnhancedColorAnalysis: true,
      enableModernFrameworkOptimization: true,
      enableThirdPartyLibraries: true,
      enableWideGamutAnalysis: true,
      enableContrastPreservationAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-037',
      'Resize Text',
      'perceivable',
      0.0535,
      'AA',
      enhancedConfig,
      this.executeResizeTextCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with text resize analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-037',
        ruleName: 'Resize Text',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'text-resize-analysis',
          zoomLevelTesting: true,
          textScalabilityValidation: true,
          functionalityPreservation: true,
          layoutAnalysis: enhancedConfig.enableLayoutAnalysis,
          enhancedColorAnalysis: enhancedConfig.enableEnhancedColorAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeResizeTextCheck(
    page: Page,
    config: ResizeTextConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced text resize analysis using optimized parallel execution
    try {
      // Use performance optimizer for parallel utility execution
      const optimizedResults = await this.performanceOptimizer.executeLayoutAnalysisOptimized(page, 'WCAG-037');
      const colorResults = await this.performanceOptimizer.executeColorAnalysisOptimized(page, 'WCAG-037');

      const resizeAnalysis = optimizedResults.contentAdaptation;
      const contrastAnalysis = colorResults.wideGamutContrast;

      // Add enhanced evidence from advanced text resize analysis
      evidence.push({
        type: 'info',
        description: 'Advanced text resize analysis with layout adaptation and contrast preservation',
        element: 'text-elements',
        value: JSON.stringify({
          resizeAnalysis: {
            overallScore: resizeAnalysis.overallScore,
            criticalIssues: resizeAnalysis.criticalIssues,
            recommendations: resizeAnalysis.recommendations,
          },
          contrastAnalysis: {
            overallScore: contrastAnalysis.overallScore,
            criticalIssues: contrastAnalysis.criticalIssues,
            recommendations: contrastAnalysis.recommendations,
          },
        }),
        severity: (resizeAnalysis.criticalIssues.length > 0 || contrastAnalysis.criticalIssues.length > 0) ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (resizeAnalysis.criticalIssues.length > 0) {
        issues.push(...resizeAnalysis.criticalIssues);
        recommendations.push(...resizeAnalysis.recommendations);
      }
      if (contrastAnalysis.criticalIssues.length > 0) {
        issues.push(...contrastAnalysis.criticalIssues);
        recommendations.push(...contrastAnalysis.recommendations);
      }

    } catch (error) {
      console.warn('Advanced text resize analysis failed, falling back to basic analysis:', error);
    }

    // Test text resizing at different zoom levels - Basic fallback analysis
    const resizeAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        fontSize: string;
        fontSizePixels: number;
        usesFixedUnits: boolean;
        hasOverflow: boolean;
        isScrollable: boolean;
        textContent: string;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Get all text-containing elements
      const textElements = document.querySelectorAll('*');
      const elementsWithText: Element[] = [];
      
      textElements.forEach(element => {
        const hasDirectText = Array.from(element.childNodes).some(
          node => node.nodeType === Node.TEXT_NODE && node.textContent?.trim()
        );
        if (hasDirectText) {
          elementsWithText.push(element);
        }
      });

      elementsWithText.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const fontSize = computedStyle.fontSize;
        const fontSizePixels = parseFloat(fontSize);
        const textContent = element.textContent?.trim() || '';
        
        // Skip if no meaningful text content
        if (textContent.length < 3) return;
        
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        // Check for fixed pixel units in font-size
        const usesFixedUnits = fontSize.includes('px') && fontSizePixels < 16;
        if (usesFixedUnits) {
          issues.push('Uses small fixed pixel font size');
          severity = 'warning';
        }
        
        // Check for very small font sizes
        if (fontSizePixels < 12) {
          issues.push('Font size below 12px');
          severity = 'error';
        }
        
        // Check for overflow issues
        const overflow = computedStyle.overflow;
        const overflowX = computedStyle.overflowX;
        const overflowY = computedStyle.overflowY;
        
        const hasOverflow = overflow === 'hidden' || 
                           overflowX === 'hidden' || 
                           overflowY === 'hidden';
        
        if (hasOverflow) {
          issues.push('Uses overflow:hidden which may clip resized text');
          severity = 'warning';
        }
        
        // Check if element is scrollable
        const isScrollable = element.scrollHeight > element.clientHeight ||
                            element.scrollWidth > element.clientWidth;
        
        // Check for fixed width/height that might prevent resizing
        const width = computedStyle.width;
        const height = computedStyle.height;
        const maxWidth = computedStyle.maxWidth;
        const maxHeight = computedStyle.maxHeight;
        
        const hasFixedDimensions = (width.includes('px') && !width.includes('auto')) ||
                                  (height.includes('px') && !height.includes('auto')) ||
                                  (maxWidth.includes('px') && maxWidth !== 'none') ||
                                  (maxHeight.includes('px') && maxHeight !== 'none');
        
        if (hasFixedDimensions && textContent.length > 20) {
          issues.push('Fixed dimensions may prevent text resizing');
          severity = 'warning';
        }
        
        // Check for absolute positioning that might cause issues
        const position = computedStyle.position;
        if (position === 'absolute' || position === 'fixed') {
          issues.push('Absolute/fixed positioning may cause resize issues');
          severity = 'info';
        }
        
        if (issues.length > 0) {
          problematicElements.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            fontSize,
            fontSizePixels,
            usesFixedUnits,
            hasOverflow,
            isScrollable,
            textContent: textContent.substring(0, 100),
            issues,
            severity,
          });
        }
      });

      // Check viewport meta tag for zoom restrictions
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      let hasZoomRestrictions = false;
      let viewportIssues: string[] = [];
      
      if (viewportMeta) {
        const content = viewportMeta.getAttribute('content') || '';
        if (content.includes('user-scalable=no') || 
            content.includes('user-scalable=0') ||
            content.includes('maximum-scale=1') ||
            content.includes('maximum-scale=1.0')) {
          hasZoomRestrictions = true;
          viewportIssues.push('Viewport meta tag prevents user scaling');
        }
      }

      // Check for CSS that might prevent zooming
      const styleSheets = Array.from(document.styleSheets);
      let hasZoomPreventingCSS = false;
      
      try {
        styleSheets.forEach(sheet => {
          if (sheet.cssRules) {
            Array.from(sheet.cssRules).forEach(rule => {
              const cssText = rule.cssText || '';
              if (cssText.includes('zoom:') || 
                  cssText.includes('transform: scale(') ||
                  cssText.includes('-webkit-text-size-adjust: none')) {
                hasZoomPreventingCSS = true;
              }
            });
          }
        });
      } catch (e) {
        // Cross-origin stylesheets may not be accessible
      }

      return {
        problematicElements,
        totalTextElements: elementsWithText.length,
        problematicCount: problematicElements.length,
        smallFontCount: problematicElements.filter(el => el.fontSizePixels < 12).length,
        fixedUnitCount: problematicElements.filter(el => el.usesFixedUnits).length,
        overflowHiddenCount: problematicElements.filter(el => el.hasOverflow).length,
        hasZoomRestrictions,
        viewportIssues,
        hasZoomPreventingCSS,
      };
    });

    let score = 100;
    const elementCount = resizeAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    // Evaluate resize text issues
    if (resizeAnalysis.hasZoomRestrictions) {
      score -= 40;
      issues.push('Viewport meta tag prevents user scaling');
      
      evidence.push({
        type: 'code',
        description: 'Viewport meta tag prevents text resizing',
        value: resizeAnalysis.viewportIssues.join(', '),
        selector: 'meta[name="viewport"]',
        elementCount: 1,
        affectedSelectors: ['meta[name="viewport"]'],
        severity: 'error',
        fixExample: {
          before: '<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">',
          after: '<meta name="viewport" content="width=device-width, initial-scale=1">',
          description: 'Remove zoom restrictions from viewport meta tag',
          codeExample: this.getCodeExample('viewport_zoom'),
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/resize-text.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/G142',
            'https://www.w3.org/WAI/WCAG21/Techniques/G179'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            hasZoomRestrictions: true,
            viewportIssues: resizeAnalysis.viewportIssues,
          },
        },
      });
    }

    if (resizeAnalysis.hasZoomPreventingCSS) {
      score -= 20;
      issues.push('CSS properties may prevent text resizing');
    }

    // Evaluate individual element issues
    resizeAnalysis.problematicElements.forEach((element) => {
      const deduction = element.severity === 'error' ? 10 : 
                       element.severity === 'warning' ? 5 : 2;
      score = Math.max(0, score - deduction);

      evidence.push({
        type: 'code',
        description: `Text resize issue: ${element.issues.join(', ')}`,
        value: `Font: ${element.fontSize}, Text: "${element.textContent}"`,
        selector: element.selector,
        elementCount: 1,
        affectedSelectors: [element.selector],
        severity: element.severity,
        fixExample: {
          before: this.getBeforeExample(element),
          after: this.getAfterExample(element),
          description: this.getFixDescription(element.issues),
          codeExample: this.getCodeExample(element.issues[0] || 'font_size'),
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/resize-text.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/C12',
            'https://www.w3.org/WAI/WCAG21/Techniques/C13'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            fontSize: element.fontSize,
            fontSizePixels: element.fontSizePixels,
            usesFixedUnits: element.usesFixedUnits,
            hasOverflow: element.hasOverflow,
            issues: element.issues,
          },
        },
      });
    });

    if (elementCount > 0) {
      issues.push(`${elementCount} elements with text resize issues found`);
      if (resizeAnalysis.smallFontCount > 0) {
        issues.push(`${resizeAnalysis.smallFontCount} elements with very small fonts`);
      }
      if (resizeAnalysis.fixedUnitCount > 0) {
        issues.push(`${resizeAnalysis.fixedUnitCount} elements using small fixed pixel fonts`);
      }
    }

    // Add recommendations
    recommendations.push('Use relative units (em, rem, %) for font sizes');
    recommendations.push('Ensure text can be resized up to 200% without loss of functionality');
    recommendations.push('Avoid fixed pixel dimensions for text containers');
    recommendations.push('Test text resizing with browser zoom and font size controls');
    recommendations.push('Remove viewport restrictions that prevent user scaling');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.issues.includes('Font size below 12px')) {
      return `<p style="font-size: ${element.fontSize};">Small text</p>`;
    }
    if (element.issues.includes('Uses small fixed pixel font size')) {
      return `<p style="font-size: ${element.fontSize};">Fixed pixel text</p>`;
    }
    if (element.issues.includes('Uses overflow:hidden')) {
      return `<div style="overflow: hidden; width: 200px;">Text that might be clipped</div>`;
    }
    return `<p style="font-size: ${element.fontSize};">Text with resize issues</p>`;
  }

  private getAfterExample(element: any): string {
    if (element.issues.includes('Font size below 12px')) {
      return '<p style="font-size: 1rem;">Readable text</p>';
    }
    if (element.issues.includes('Uses small fixed pixel font size')) {
      return '<p style="font-size: 1em;">Scalable text</p>';
    }
    if (element.issues.includes('Uses overflow:hidden')) {
      return '<div style="overflow: auto; max-width: 200px;">Text that can expand</div>';
    }
    return '<p style="font-size: 1rem;">Properly sized text</p>';
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('Font size below 12px')) {
      return 'Increase font size to at least 12px or use relative units';
    }
    if (issues.includes('Uses small fixed pixel font size')) {
      return 'Use relative units (em, rem) instead of small pixel values';
    }
    if (issues.includes('Uses overflow:hidden')) {
      return 'Use overflow:auto or remove overflow restrictions';
    }
    return 'Use relative units and avoid restrictions that prevent text resizing';
  }

  private getCodeExample(issueType: string): string {
    switch (issueType) {
      case 'viewport_zoom':
        return `
<!-- Before: Prevents user scaling -->
<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, maximum-scale=1">

<!-- After: Allows user scaling -->
<meta name="viewport" content="width=device-width, initial-scale=1">
        `;
      case 'Font size below 12px':
        return `
/* Before: Very small fixed font */
.small-text {
  font-size: 10px;
}

/* After: Relative font size */
.readable-text {
  font-size: 0.875rem; /* 14px at default size */
  min-font-size: 12px;
}
        `;
      case 'Uses small fixed pixel font size':
        return `
/* Before: Fixed pixel fonts */
.text {
  font-size: 12px;
  width: 300px;
  height: 200px;
}

/* After: Relative units */
.text {
  font-size: 1rem;
  width: 20em;
  max-height: 15em;
  overflow: auto;
}
        `;
      case 'Uses overflow:hidden which may clip resized text':
        return `
/* Before: Hidden overflow clips text */
.container {
  width: 200px;
  height: 100px;
  overflow: hidden;
}

/* After: Scrollable container */
.container {
  max-width: 200px;
  max-height: 100px;
  overflow: auto;
}
        `;
      default:
        return 'Use relative units (em, rem, %) and avoid restrictions that prevent text resizing to 200%';
    }
  }
}
