/**
 * WCAG Rule 5: Keyboard - 2.1.1
 * 85% Automated - Manual review for complex interactions
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { ManualReviewItem } from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface InteractiveElementData {
  selector: string;
  tagName: string;
  type?: string;
  role?: string;
  tabIndex: number;
  isVisible: boolean;
  hasKeyboardHandlers: boolean;
  isNativelyFocusable: boolean;
  ariaLabel?: string;
  textContent: string;
}

export interface KeyboardConfig extends EnhancedCheckConfig {
  enableAdvancedKeyboardTesting?: boolean;
  enableFocusTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableFrameworkOptimization?: boolean;
  enableComplexInteractionAnalysis?: boolean;
}

export class KeyboardCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform keyboard accessibility check - 85% automated with enhanced evidence
   */
  async performCheck(config: KeyboardConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: KeyboardConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableAdvancedKeyboardTesting: true,
      enableFocusTracking: true,
      enableAccessibilityPatterns: true,
      enableFrameworkOptimization: true,
      enableComplexInteractionAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-005',
      'Keyboard',
      'operable',
      0.12,
      'A',
      enhancedConfig,
      this.executeKeyboardCheck.bind(this),
      true, // Requires browser
      true, // Manual review for complex interactions
    );

    return result;
  }

  /**
   * Execute comprehensive keyboard accessibility analysis
   */
  private async executeKeyboardCheck(page: Page, _config: KeyboardConfig) {
    const startTime = Date.now();
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];
    let elementsAnalyzed = 0;

    // Get all interactive elements
    const interactiveElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        tagName: string;
        type?: string;
        role?: string;
        tabIndex: number;
        isVisible: boolean;
        hasKeyboardHandlers: boolean;
        isNativelyFocusable: boolean;
        ariaLabel?: string;
        textContent: string;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function isNativelyFocusable(element: Element): boolean {
        const tagName = element.tagName.toLowerCase();
        const type = (element as HTMLInputElement).type?.toLowerCase();

        // Native focusable elements
        if (['a', 'button', 'input', 'select', 'textarea'].includes(tagName)) {
          if (tagName === 'input' && type === 'hidden') return false;
          if (tagName === 'a' && !(element as HTMLAnchorElement).href) return false;
          return true;
        }

        // Elements with tabindex
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex !== null) {
          return parseInt(tabIndex) >= 0;
        }

        return false;
      }

      function hasKeyboardEventHandlers(element: Element): boolean {
        // Check for keyboard event listeners (this is limited in evaluation context)
        const events = ['onkeydown', 'onkeyup', 'onkeypress'];
        return events.some((event) => element.getAttribute(event) !== null);
      }

      // Find all potentially interactive elements
      const allElements = document.querySelectorAll('*');
      const interactiveSelectors = [
        'a',
        'button',
        'input',
        'select',
        'textarea',
        'details',
        'summary',
        '[role="button"]',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="checkbox"]',
        '[role="radio"]',
        '[role="slider"]',
        '[role="spinbutton"]',
        '[onclick]',
        '[onkeydown]',
        '[onkeyup]',
        '[tabindex]',
      ];

      allElements.forEach((element, index) => {
        const isInteractive = interactiveSelectors.some((selector) => {
          try {
            return element.matches(selector);
          } catch {
            return false;
          }
        });

        if (isInteractive) {
          const computedStyle = window.getComputedStyle(element);
          const isVisible =
            computedStyle.display !== 'none' &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.opacity !== '0';

          if (isVisible) {
            const tabIndex = element.getAttribute('tabindex');

            elements.push({
              selector: generateSelector(element, index),
              tagName: element.tagName.toLowerCase(),
              type: (element as HTMLInputElement).type,
              role: element.getAttribute('role') || undefined,
              tabIndex: tabIndex ? parseInt(tabIndex) : isNativelyFocusable(element) ? 0 : -1,
              isVisible,
              hasKeyboardHandlers: hasKeyboardEventHandlers(element),
              isNativelyFocusable: isNativelyFocusable(element),
              ariaLabel: element.getAttribute('aria-label') || undefined,
              textContent: element.textContent?.trim().substring(0, 50) || '',
            });
          }
        }
      });

      return elements;
    });

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    // Analyze each interactive element
    for (const element of interactiveElements) {
      totalChecks++;
      elementsAnalyzed++;

      const analysis = this.analyzeKeyboardAccessibility(element);

      if (analysis.status === 'passed') {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Element is keyboard accessible',
          value: analysis.reason,
          selector: element.selector,
          severity: 'info',
        });
      } else if (analysis.status === 'failed') {
        issues.push(`Keyboard accessibility issue: ${element.selector}`);
        evidence.push({
          type: 'text',
          description: 'Element has keyboard accessibility issues',
          value: analysis.reason,
          selector: element.selector,
          severity: 'error',
        });
        recommendations.push(`${element.selector}: ${analysis.recommendation}`);
      } else if (analysis.status === 'manual_review') {
        manualReviewCount++;
        manualReviewItems.push({
          selector: element.selector,
          description: 'Complex keyboard interaction verification needed',
          automatedFindings: analysis.reason,
          reviewRequired:
            'Test keyboard navigation and verify all functionality is accessible via keyboard',
          priority:
            element.role?.includes('menu') || element.tagName === 'select' ? 'high' : 'medium',
          estimatedTime: 3,
        });

        evidence.push({
          type: 'text',
          description: 'Element requires manual keyboard testing',
          value: analysis.reason,
          selector: element.selector,
          severity: 'warning',
        });
      }
    }

    // Test basic keyboard navigation
    const navigationTest = await this.testKeyboardNavigation(page);
    totalChecks += navigationTest.totalChecks;
    passedChecks += navigationTest.passedChecks;
    evidence.push(...navigationTest.evidence);
    issues.push(...navigationTest.issues);
    recommendations.push(...navigationTest.recommendations);
    manualReviewItems.push(...navigationTest.manualReviewItems);
    manualReviewCount += navigationTest.manualReviewItems.length;

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Keyboard accessibility analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.85,
    };
  }

  /**
   * Analyze individual element for keyboard accessibility
   */
  private analyzeKeyboardAccessibility(element: InteractiveElementData): {
    status: 'passed' | 'failed' | 'manual_review';
    reason: string;
    recommendation?: string;
  } {
    // Check if element should be focusable
    const shouldBeFocusable =
      element.isNativelyFocusable ||
      element.hasKeyboardHandlers ||
      ['button', 'link', 'menuitem', 'tab'].includes(element.role || '');

    if (!shouldBeFocusable) {
      return {
        status: 'passed',
        reason: 'Element does not require keyboard focus',
      };
    }

    // Check if element is focusable
    if (element.tabIndex < 0 && !element.isNativelyFocusable) {
      return {
        status: 'failed',
        reason: 'Interactive element is not keyboard focusable',
        recommendation: 'Add tabindex="0" or use focusable element',
      };
    }

    // Check for custom interactive elements
    if (element.hasKeyboardHandlers && !element.isNativelyFocusable && element.tabIndex < 0) {
      return {
        status: 'failed',
        reason: 'Custom interactive element lacks keyboard focus',
        recommendation: 'Add tabindex="0" and proper keyboard event handlers',
      };
    }

    // Elements that require manual testing for complex interactions
    const complexElements = ['select', 'details', 'menu', 'combobox', 'listbox', 'tree', 'grid'];
    if (
      complexElements.includes(element.tagName) ||
      complexElements.some((role) => element.role?.includes(role))
    ) {
      return {
        status: 'manual_review',
        reason: `Complex interactive element (${element.tagName}${element.role ? `, role: ${element.role}` : ''}) requires manual keyboard testing`,
      };
    }

    return {
      status: 'passed',
      reason: 'Element appears to be keyboard accessible',
    };
  }

  /**
   * Test basic keyboard navigation
   */
  private async testKeyboardNavigation(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    const evidence: Array<{
      type:
        | 'text'
        | 'image'
        | 'code'
        | 'measurement'
        | 'interaction'
        | 'info'
        | 'warning'
        | 'error';
      description: string;
      value: string;
      selector?: string;
      severity?: 'info' | 'warning' | 'error' | 'critical';
    }> = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: Array<{
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }> = [];
    const totalChecks = 2;
    let passedChecks = 0;

    try {
      // Test Tab navigation
      await page.keyboard.press('Tab');
      const activeElement = await page.evaluate(() => {
        const active = document.activeElement;
        return active
          ? {
              tagName: active.tagName.toLowerCase(),
              id: active.id,
              className: active.className,
            }
          : null;
      });

      if (activeElement) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Tab navigation is functional',
          value: `Focus moved to: ${activeElement.tagName}${activeElement.id ? `#${activeElement.id}` : ''}`,
          severity: 'info',
        });
      } else {
        issues.push('Tab navigation does not move focus to any element');
        recommendations.push('Ensure focusable elements exist and are properly configured');
      }

      // Test Shift+Tab navigation
      await page.keyboard.down('Shift');
      await page.keyboard.press('Tab');
      await page.keyboard.up('Shift');

      const shiftTabElement = await page.evaluate(() => {
        const active = document.activeElement;
        return active ? active.tagName.toLowerCase() : null;
      });

      if (shiftTabElement) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Reverse tab navigation is functional',
          value: 'Shift+Tab successfully moves focus backwards',
          severity: 'info',
        });
      } else {
        issues.push('Shift+Tab navigation does not work properly');
        recommendations.push('Ensure reverse tab navigation is supported');
      }
    } catch (error) {
      issues.push('Error testing keyboard navigation');
      recommendations.push('Manual keyboard navigation testing required');

      manualReviewItems.push({
        selector: 'body',
        description: 'Keyboard navigation testing failed',
        automatedFindings: 'Automated keyboard testing encountered errors',
        reviewRequired:
          'Manually test Tab, Shift+Tab, and arrow key navigation throughout the page',
        priority: 'high',
        estimatedTime: 5,
      });
    }

    // Enhanced evidence standardization with keyboard-specific analysis
    const scanDuration = Date.now() - startTime;
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      evidence,
      {
        ruleId: 'WCAG-005',
        ruleName: 'Keyboard',
        scanDuration,
        elementsAnalyzed,
        checkSpecificData: {
          totalInteractiveElements: totalChecks,
          keyboardAccessibleElements: passedChecks,
          automationRate: 0.85,
          checkType: 'keyboard-accessibility',
          interactionPatterns: true,
          focusManagement: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8, // High threshold for keyboard accessibility
        maxEvidenceItems: 40,
      }
    );

    return {
      totalChecks,
      passedChecks,
      evidence: enhancedEvidence,
      issues,
      recommendations,
      manualReviewItems
    };
  }
}
