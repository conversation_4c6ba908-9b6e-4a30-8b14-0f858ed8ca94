/**
 * Smart Caching System for WCAG Scanning
 * Multi-layer caching for DOM analysis, rule results, and common website patterns
 */

import crypto from 'crypto';
import { getPerformanceConfig } from '../../../config/performance';
import logger from '../../../utils/logger';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  hash: string;
  accessCount: number;
  lastAccessed: number;
  size: number; // Estimated size in bytes
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in MB
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableCompression: boolean; // Enable data compression
}

/**
 * Multi-layer smart cache with LRU eviction and intelligent prefetching
 */
export class SmartCache {
  private static instance: SmartCache;
  private domCache = new Map<string, CacheEntry<any>>();
  private ruleCache = new Map<string, CacheEntry<any>>();
  private patternCache = new Map<string, CacheEntry<any>>();
  private siteCache = new Map<string, CacheEntry<any>>();

  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
  };

  private config: CacheConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private performanceConfig = getPerformanceConfig();

  private constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: config?.maxSize || 100, // 100MB default
      maxEntries: config?.maxEntries || this.performanceConfig.maxCacheSize,
      defaultTTL: config?.defaultTTL || this.performanceConfig.cacheExpiryMinutes * 60 * 1000,
      cleanupInterval: config?.cleanupInterval || 300000, // 5 minutes
      enableCompression: config?.enableCompression ?? true,
    };

    this.startCleanupInterval();
    logger.info('🗄️ Smart cache initialized', {
      maxSize: this.config.maxSize,
      maxEntries: this.config.maxEntries,
      defaultTTL: this.config.defaultTTL,
    });
  }

  static getInstance(config?: Partial<CacheConfig>): SmartCache {
    if (!SmartCache.instance) {
      SmartCache.instance = new SmartCache(config);
    }
    return SmartCache.instance;
  }

  /**
   * Get cached DOM analysis result
   */
  async getDOMAnalysis<T>(url: string, selector: string, contentHash?: string): Promise<T | null> {
    const key = this.generateDOMKey(url, selector, contentHash);
    return this.get<T>(key, 'dom');
  }

  /**
   * Cache DOM analysis result
   */
  async cacheDOMAnalysis<T>(
    url: string,
    selector: string,
    data: T,
    contentHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateDOMKey(url, selector, contentHash);
    await this.set(key, data, 'dom', ttl);
  }

  /**
   * Get cached utility analysis result (Performance Optimization)
   */
  async getUtilityAnalysis<T>(url: string, cacheKey: string): Promise<T | null> {
    return this.get<T>(cacheKey, 'pattern');
  }

  /**
   * Cache utility analysis result (Performance Optimization)
   */
  async cacheUtilityAnalysis<T>(
    url: string,
    cacheKey: string,
    data: T,
    ttl?: number,
  ): Promise<void> {
    await this.set(cacheKey, data, 'pattern', ttl);
  }

  /**
   * Get cached rule result
   */
  async getRuleResult<T>(
    ruleId: string,
    contentHash: string,
    configHash?: string,
  ): Promise<T | null> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    return this.get<T>(key, 'rule');
  }

  /**
   * Cache rule result
   */
  async cacheRuleResult<T>(
    ruleId: string,
    contentHash: string,
    data: T,
    configHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    await this.set(key, data, 'rule', ttl);
  }

  /**
   * Get cached pattern recognition result
   */
  async getPattern<T>(patternType: string, contentHash: string): Promise<T | null> {
    const key = this.generatePatternKey(patternType, contentHash);
    return this.get<T>(key, 'pattern');
  }

  /**
   * Cache pattern recognition result
   */
  async cachePattern<T>(patternType: string, contentHash: string, data: T): Promise<void> {
    const key = this.generatePatternKey(patternType, contentHash);
    // Patterns have longer TTL as they're more stable
    await this.set(key, data, 'pattern', this.config.defaultTTL * 2);
  }

  /**
   * Get cached site-wide analysis
   */
  async getSiteAnalysis<T>(url: string, analysisType: string): Promise<T | null> {
    const key = this.generateSiteKey(url, analysisType);
    return this.get<T>(key, 'site');
  }

  /**
   * Cache site-wide analysis
   */
  async cacheSiteAnalysis<T>(
    url: string,
    analysisType: string,
    data: T,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateSiteKey(url, analysisType);
    await this.set(key, data, 'site', ttl);
  }

  /**
   * Generic get method
   */
  private async get<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
  ): Promise<T | null> {
    const cache = this.getCache(cacheType);
    const entry = cache.get(key);

    if (!entry) {
      this.stats.misses++;
      logger.debug(`🔍 Cache miss: ${cacheType}:${key}`);
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.config.defaultTTL) {
      cache.delete(key);
      this.stats.misses++;
      logger.debug(`⏰ Cache expired: ${cacheType}:${key}`);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    logger.debug(`✅ Cache hit: ${cacheType}:${key} (accessed ${entry.accessCount} times)`);
    return entry.data as T;
  }

  /**
   * Generic set method
   */
  private async set<T>(
    key: string,
    data: T,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    ttl?: number,
  ): Promise<void> {
    const cache = this.getCache(cacheType);
    const now = Date.now();
    const dataSize = this.estimateSize(data);

    // Check if we need to evict entries
    await this.ensureCapacity(cache, dataSize);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      hash: this.generateHash(data),
      accessCount: 1,
      lastAccessed: now,
      size: dataSize,
    };

    cache.set(key, entry);
    logger.debug(`💾 Cached: ${cacheType}:${key} (${dataSize} bytes)`);
  }

  /**
   * Ensure cache has capacity for new entry
   */
  private async ensureCapacity(
    cache: Map<string, CacheEntry<any>>,
    newEntrySize: number,
  ): Promise<void> {
    const currentSize = this.calculateCacheSize(cache);
    const maxSizeBytes = this.config.maxSize * 1024 * 1024; // Convert MB to bytes

    // Check size limit
    if (currentSize + newEntrySize > maxSizeBytes) {
      await this.evictLRU(cache, newEntrySize);
    }

    // Check entry count limit
    if (cache.size >= this.config.maxEntries) {
      await this.evictLRU(cache, 0);
    }
  }

  /**
   * Evict least recently used entries
   */
  private async evictLRU(
    cache: Map<string, CacheEntry<any>>,
    requiredSpace: number,
  ): Promise<void> {
    const entries = Array.from(cache.entries());

    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    let freedSpace = 0;
    let evictedCount = 0;

    for (const [key, entry] of entries) {
      cache.delete(key);
      freedSpace += entry.size;
      evictedCount++;
      this.stats.evictions++;

      // Stop if we've freed enough space and are under entry limit
      if (freedSpace >= requiredSpace && cache.size < this.config.maxEntries) {
        break;
      }
    }

    logger.debug(`🗑️ Evicted ${evictedCount} entries, freed ${freedSpace} bytes`);
  }

  /**
   * Calculate total cache size in bytes
   */
  private calculateCacheSize(cache: Map<string, CacheEntry<unknown>>): number {
    let totalSize = 0;
    for (const entry of cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Estimate object size in bytes
   */
  private estimateSize(obj: unknown): number {
    const jsonString = JSON.stringify(obj);
    return Buffer.byteLength(jsonString, 'utf8');
  }

  /**
   * Get cache instance by type
   */
  private getCache(type: 'dom' | 'rule' | 'pattern' | 'site'): Map<string, CacheEntry<unknown>> {
    switch (type) {
      case 'dom':
        return this.domCache;
      case 'rule':
        return this.ruleCache;
      case 'pattern':
        return this.patternCache;
      case 'site':
        return this.siteCache;
      default:
        throw new Error(`Unknown cache type: ${type}`);
    }
  }

  /**
   * Generate cache keys
   */
  private generateDOMKey(url: string, selector: string, contentHash?: string): string {
    const baseKey = `${this.normalizeUrl(url)}:${selector}`;
    return contentHash ? `${baseKey}:${contentHash}` : baseKey;
  }

  private generateRuleKey(ruleId: string, contentHash: string, configHash?: string): string {
    const baseKey = `${ruleId}:${contentHash}`;
    return configHash ? `${baseKey}:${configHash}` : baseKey;
  }

  private generatePatternKey(patternType: string, contentHash: string): string {
    return `${patternType}:${contentHash}`;
  }

  private generateSiteKey(url: string, analysisType: string): string {
    return `${this.normalizeUrl(url)}:${analysisType}`;
  }

  /**
   * Normalize URL for consistent caching
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      // Remove query parameters and fragments for more cache hits
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }

  /**
   * Generate hash for data integrity
   */
  private generateHash(data: any): string {
    return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Perform cache cleanup
   */
  private performCleanup(): void {
    const now = Date.now();
    let totalCleaned = 0;

    const caches = [
      { name: 'dom', cache: this.domCache },
      { name: 'rule', cache: this.ruleCache },
      { name: 'pattern', cache: this.patternCache },
      { name: 'site', cache: this.siteCache },
    ];

    for (const { name, cache } of caches) {
      let cleaned = 0;
      for (const [key, entry] of cache.entries()) {
        if (now - entry.timestamp > this.config.defaultTTL) {
          cache.delete(key);
          cleaned++;
        }
      }
      totalCleaned += cleaned;
      if (cleaned > 0) {
        logger.debug(`🧹 Cleaned ${cleaned} expired entries from ${name} cache`);
      }
    }

    if (totalCleaned > 0) {
      logger.info(`🧹 Cache cleanup completed: ${totalCleaned} entries removed`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalEntries =
      this.domCache.size + this.ruleCache.size + this.patternCache.size + this.siteCache.size;

    const totalSize =
      this.calculateCacheSize(this.domCache) +
      this.calculateCacheSize(this.ruleCache) +
      this.calculateCacheSize(this.patternCache) +
      this.calculateCacheSize(this.siteCache);

    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    const missRate = totalRequests > 0 ? (this.stats.misses / totalRequests) * 100 : 0;

    // Find oldest and newest entries
    let oldestEntry = Date.now();
    let newestEntry = 0;

    const allCaches = [this.domCache, this.ruleCache, this.patternCache, this.siteCache];
    for (const cache of allCaches) {
      for (const entry of cache.values()) {
        oldestEntry = Math.min(oldestEntry, entry.timestamp);
        newestEntry = Math.max(newestEntry, entry.timestamp);
      }
    }

    return {
      totalEntries,
      totalSize,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      evictionCount: this.stats.evictions,
      oldestEntry: oldestEntry === Date.now() ? 0 : oldestEntry,
      newestEntry,
    };
  }

  /**
   * Clear specific cache type
   */
  clearCache(type: 'dom' | 'rule' | 'pattern' | 'site' | 'all'): void {
    if (type === 'all') {
      this.domCache.clear();
      this.ruleCache.clear();
      this.patternCache.clear();
      this.siteCache.clear();
      logger.info('🗑️ All caches cleared');
    } else {
      this.getCache(type).clear();
      logger.info(`🗑️ ${type} cache cleared`);
    }
  }

  /**
   * Warm up cache with common patterns
   */
  async warmupCache(commonUrls: string[]): Promise<void> {
    logger.info(`🔥 Warming up cache with ${commonUrls.length} URLs`);

    // This would be implemented to pre-populate cache with common patterns
    // For now, just log the intent
    for (const url of commonUrls) {
      logger.debug(`🔥 Would warm up cache for: ${url}`);
    }
  }

  /**
   * Shutdown cache
   */
  shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.clearCache('all');
    this.stats = { hits: 0, misses: 0, evictions: 0 };

    logger.info('🔄 Smart cache shutdown completed');
  }
}

export default SmartCache;
