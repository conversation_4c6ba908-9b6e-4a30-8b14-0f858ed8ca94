/**
 * WCAG-025: Landmarks Check
 * Success Criterion: 2.4.1 Bypass Blocks (Level A) and general landmark usage
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface LandmarkAnalysis {
  hasMain: boolean;
  hasNav: boolean;
  hasHeader: boolean;
  hasFooter: boolean;
  hasAside: boolean;
  mainCount: number;
  navCount: number;
  headerCount: number;
  footerCount: number;
  asideCount: number;
  contentWithoutLandmarks: number;
  totalContentElements: number;
  landmarkElements: Array<{
    tagName: string;
    role?: string;
    selector: string;
    hasAccessibleName: boolean;
    accessibleName?: string;
  }>;
}

export interface LandmarksConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class LandmarksCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: LandmarksConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LandmarksConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-025',
      'Landmarks',
      'operable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeLandmarksCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with landmark analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-025',
        ruleName: 'Landmarks',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.95,
          checkType: 'landmark-analysis',
          semanticStructureAnalysis: true,
          ariaLandmarkAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.9,
        maxEvidenceItems: 25,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'landmark-detection',
        confidence: 0.95,
        additionalData: {
          checkType: 'structural-analysis',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeLandmarksCheck(
    page: Page,
    config: LandmarksConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze landmark structure
    const landmarkAnalysis = await page.evaluate((): LandmarkAnalysis => {
      const landmarkSelectors = [
        'main, [role="main"]',
        'nav, [role="navigation"]', 
        'header, [role="banner"]',
        'footer, [role="contentinfo"]',
        'aside, [role="complementary"]'
      ];

      const landmarks = document.querySelectorAll(landmarkSelectors.join(', '));
      const landmarkElements: Array<{
        tagName: string;
        role?: string;
        selector: string;
        hasAccessibleName: boolean;
        accessibleName?: string;
      }> = [];

      landmarks.forEach((element, index) => {
        const tagName = element.tagName.toLowerCase();
        const role = element.getAttribute('role');
        const ariaLabel = element.getAttribute('aria-label');
        const ariaLabelledby = element.getAttribute('aria-labelledby');
        
        let accessibleName = '';
        if (ariaLabel) {
          accessibleName = ariaLabel;
        } else if (ariaLabelledby) {
          const labelElement = document.getElementById(ariaLabelledby);
          accessibleName = labelElement?.textContent?.trim() || '';
        }

        landmarkElements.push({
          tagName,
          role: role || undefined,
          selector: `${tagName}:nth-of-type(${index + 1})`,
          hasAccessibleName: !!accessibleName,
          accessibleName: accessibleName || undefined,
        });
      });

      // Count specific landmark types
      const mainElements = document.querySelectorAll('main, [role="main"]');
      const navElements = document.querySelectorAll('nav, [role="navigation"]');
      const headerElements = document.querySelectorAll('header, [role="banner"]');
      const footerElements = document.querySelectorAll('footer, [role="contentinfo"]');
      const asideElements = document.querySelectorAll('aside, [role="complementary"]');

      // Count content elements that should be in landmarks
      const contentElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, article, section, div');
      const contentWithoutLandmarks = Array.from(contentElements).filter(el => {
        // Check if element is inside a landmark
        return !el.closest('main, nav, header, footer, aside, [role="main"], [role="navigation"], [role="banner"], [role="contentinfo"], [role="complementary"]');
      }).length;

      return {
        hasMain: mainElements.length > 0,
        hasNav: navElements.length > 0,
        hasHeader: headerElements.length > 0,
        hasFooter: footerElements.length > 0,
        hasAside: asideElements.length > 0,
        mainCount: mainElements.length,
        navCount: navElements.length,
        headerCount: headerElements.length,
        footerCount: footerElements.length,
        asideCount: asideElements.length,
        contentWithoutLandmarks,
        totalContentElements: contentElements.length,
        landmarkElements,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalLandmarks = landmarkAnalysis.landmarkElements.length;

    // Check for missing main landmark
    if (!landmarkAnalysis.hasMain) {
      score -= 40; // Major penalty for missing main
      issues.push('Missing main landmark element');
      
      evidence.push({
        type: 'error',
        description: 'Page missing main landmark',
        value: 'No <main> element or role="main" found',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: ['body'],
        severity: 'error',
        fixExample: {
          before: '<body><div>Content without landmarks</div></body>',
          after: '<body><main><div>Main content within landmark</div></main></body>',
          description: 'Wrap main content in <main> element',
          codeExample: `
<!-- Before -->
<body>
  <div>Main page content</div>
</body>

<!-- After -->
<body>
  <main>
    <div>Main page content</div>
  </main>
</body>
          `,
          resources: [
            'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/',
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/main'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            missingLandmark: 'main',
            totalLandmarks,
          },
        },
      });
      
      recommendations.push('Add a <main> element to contain the primary content');
    }

    // Check for multiple main landmarks
    if (landmarkAnalysis.mainCount > 1) {
      score -= 20;
      issues.push(`Multiple main landmarks found (${landmarkAnalysis.mainCount})`);
      
      evidence.push({
        type: 'warning',
        description: 'Multiple main landmarks detected',
        value: `Found ${landmarkAnalysis.mainCount} main landmarks`,
        selector: 'main, [role="main"]',
        elementCount: landmarkAnalysis.mainCount,
        affectedSelectors: ['main', '[role="main"]'],
        severity: 'warning',
        fixExample: {
          before: '<main>Content 1</main><main>Content 2</main>',
          after: '<main>Content 1<section>Content 2</section></main>',
          description: 'Use only one main landmark per page',
          resources: [
            'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: landmarkAnalysis.mainCount,
          checkSpecificData: {
            duplicateLandmark: 'main',
            count: landmarkAnalysis.mainCount,
          },
        },
      });
      
      recommendations.push('Use only one main landmark per page');
    }

    // Check for missing navigation
    if (!landmarkAnalysis.hasNav && landmarkAnalysis.totalContentElements > 10) {
      score -= 15;
      issues.push('Missing navigation landmark for content-rich page');
      
      evidence.push({
        type: 'warning',
        description: 'Content-rich page missing navigation landmark',
        value: `Page has ${landmarkAnalysis.totalContentElements} content elements but no navigation`,
        selector: 'body',
        elementCount: 0,
        affectedSelectors: ['nav', '[role="navigation"]'],
        severity: 'warning',
        fixExample: {
          before: '<div class="menu">Navigation links</div>',
          after: '<nav>Navigation links</nav>',
          description: 'Use <nav> element for navigation sections',
          resources: [
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/nav'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            missingLandmark: 'nav',
            contentElements: landmarkAnalysis.totalContentElements,
          },
        },
      });
      
      recommendations.push('Add <nav> elements for navigation sections');
    }

    // Check for content outside landmarks
    if (landmarkAnalysis.contentWithoutLandmarks > 0) {
      const penalty = Math.min(25, landmarkAnalysis.contentWithoutLandmarks * 2);
      score -= penalty;
      issues.push(`${landmarkAnalysis.contentWithoutLandmarks} content elements outside landmarks`);
      
      evidence.push({
        type: 'warning',
        description: 'Content elements outside landmark regions',
        value: `${landmarkAnalysis.contentWithoutLandmarks} elements not in landmarks`,
        selector: 'p, h1, h2, h3, h4, h5, h6, article, section, div',
        elementCount: landmarkAnalysis.contentWithoutLandmarks,
        affectedSelectors: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'article', 'section', 'div'],
        severity: 'warning',
        fixExample: {
          before: '<body><div>Orphaned content</div></body>',
          after: '<body><main><div>Content in landmark</div></main></body>',
          description: 'Wrap content in appropriate landmark elements',
          resources: [
            'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: landmarkAnalysis.contentWithoutLandmarks,
          checkSpecificData: {
            orphanedContent: landmarkAnalysis.contentWithoutLandmarks,
            totalContent: landmarkAnalysis.totalContentElements,
          },
        },
      });
      
      recommendations.push('Move content into appropriate landmark regions');
    }

    // Add positive evidence for good landmark usage
    if (score > 80) {
      evidence.push({
        type: 'info',
        description: 'Good landmark structure detected',
        value: `Found ${totalLandmarks} landmarks with proper structure`,
        selector: 'main, nav, header, footer, aside',
        elementCount: totalLandmarks,
        affectedSelectors: ['main', 'nav', 'header', 'footer', 'aside'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: totalLandmarks,
          checkSpecificData: {
            landmarkTypes: {
              main: landmarkAnalysis.mainCount,
              nav: landmarkAnalysis.navCount,
              header: landmarkAnalysis.headerCount,
              footer: landmarkAnalysis.footerCount,
              aside: landmarkAnalysis.asideCount,
            },
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    if (score < 100) {
      recommendations.push('Use semantic HTML5 landmark elements (main, nav, header, footer, aside)');
      recommendations.push('Ensure all content is contained within appropriate landmarks');
      recommendations.push('Provide accessible names for multiple landmarks of the same type');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
